﻿import React from "react";
import {
  FormControlLabel,
  FormHelperText,
  Radio,
  RadioGroup,
  RadioGroupProps,
} from "@mui/material";

import MultiSelectModel from "../../models/MultiSelectModel";

interface IRadioButtonProps extends RadioGroupProps {
  helperText?: string;
  error?: boolean;
  color?: "primary" | "secondary" | "default";
  options: Array<MultiSelectModel>;
}

/** Renders Radio Button Group Control */
const RadioButtonGroup = (props: IRadioButtonProps) => {
  const { id, name, helperText, error, options, color } = props;
  return (
    <>
      <RadioGroup
        id={id}
        aria-label={name}
        name={name}
        {...props}
        className="d-block"
      >
        {options.map((option: MultiSelectModel) => (
          <FormControlLabel
            key={`radio_${option.id}`}
            value={String(option.id)}
            control={<Radio color={color} className="radiobox-control" />}
            label={option.text}
            disabled={option.disabled || false}
            className="col-radiobox-control me-4"
          />
        ))}
      </RadioGroup>

      {helperText && (
        <FormHelperText
          className={`${
            error &&
            "MuiFormHelperText-root MuiFormHelperText-contained Mui-error"
          }`}
        >
          {helperText}
        </FormHelperText>
      )}
    </>
  );
};

export default RadioButtonGroup;

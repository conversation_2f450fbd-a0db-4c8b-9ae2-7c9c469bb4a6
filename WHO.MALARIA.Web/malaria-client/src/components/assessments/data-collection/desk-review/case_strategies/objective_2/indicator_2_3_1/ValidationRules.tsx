import { DataType } from "../../../../../../../models/Enums";
import { Constants } from "../../../../../../../models/Constants";
import ValidationRuleModel, {
    IValidationRuleProvider,
} from "../../../../../../../models/ValidationRuleModel";

export const GuidelineValidationRules: IValidationRuleProvider = {
    guideline1: new ValidationRuleModel(DataType.Object, false),
    "guideline1.guideLineName": new ValidationRuleModel(
        DataType.String,
        false,
        `const guideLineNameForData=Object.keys(${Constants.Common.RootObjectNameSubstitute}).filter((key)=>!["cannotBeAssessed","cannotBeAssessedReason", "linkToCopy", "metNotMetStatus"].includes(key));
         guideLineNameForData.every(data => isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}[data].guideLineName ))`
    ),
}

const ValidationRules: IValidationRuleProvider = {
    guideline1: new ValidationRuleModel(DataType.Object, false),
    "guideline1.guideLineName": new ValidationRuleModel(
        DataType.String,
        false,
        `const guideLineNameForData = Object.keys(${Constants.Common.RootObjectNameSubstitute}).filter((key) => !["cannotBeAssessed", "cannotBeAssessedReason", "linkToCopy", "metNotMetStatus"].includes(key));
    guideLineNameForData.every(data => isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}[data].guideLineName))`
    ),

    healthSector: new ValidationRuleModel(DataType.Object, false),
    "healthSector.publicHealthSector": new ValidationRuleModel(DataType.Object, true),
    "healthSector.privateFormal": new ValidationRuleModel(DataType.Object, true),
    "healthSector.privateInformal": new ValidationRuleModel(DataType.Object, false),
    "healthSector.community": new ValidationRuleModel(DataType.Object, false),

    //"healthSector.publicHealthSector.publicReportingDetails": new ValidationRuleModel(DataType.Object, false),
    "healthSector.publicHealthSector.healthFacility": new ValidationRuleModel(DataType.Object, false),
    "healthSector.publicHealthSector.hospital": new ValidationRuleModel(DataType.Object, false),
    "healthSector.publicHealthSector.laboratory": new ValidationRuleModel(DataType.Object, false),
    // "healthSector.publicHealthSector.publicReportingDetails.isFirstGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['publicReportingDetails']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.publicReportingDetails.isFirstGuidelineReceived === undefined`),
    // "healthSector.publicHealthSector.publicReportingDetails.firstGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['publicReportingDetails']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.publicReportingDetails.firstGuidelineDetails)`),
    "healthSector.publicHealthSector.healthFacility.isFirstGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.healthFacility.isFirstGuidelineReceived === undefined`),
    "healthSector.publicHealthSector.healthFacility.firstGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.healthFacility.isFirstGuidelineReceived !== null && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.healthFacility.firstGuidelineDetails)`),
    "healthSector.publicHealthSector.hospital.isFirstGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.hospital.isFirstGuidelineReceived === undefined`),
    "healthSector.publicHealthSector.hospital.firstGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.hospital.isFirstGuidelineReceived !== null && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.hospital.firstGuidelineDetails)`),
    "healthSector.publicHealthSector.laboratory.isFirstGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.laboratory.isFirstGuidelineReceived === undefined`),
    "healthSector.publicHealthSector.laboratory.firstGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.laboratory.isFirstGuidelineReceived !== null && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.laboratory.firstGuidelineDetails)`),
    // "healthSector.publicHealthSector.publicReportingDetails.isSecondGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['publicReportingDetails']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.publicReportingDetails.isSecondGuidelineReceived === null`),
    // "healthSector.publicHealthSector.publicReportingDetails.secondGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['publicReportingDetails']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.publicReportingDetails.secondGuidelineDetails)`),
    "healthSector.publicHealthSector.healthFacility.isSecondGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.healthFacility.isSecondGuidelineReceived === null`),
    "healthSector.publicHealthSector.healthFacility.secondGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.healthFacility.secondGuidelineDetails)`),
    "healthSector.publicHealthSector.hospital.isSecondGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.hospital.isSecondGuidelineReceived === null`),
    "healthSector.publicHealthSector.hospital.secondGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.hospital.secondGuidelineDetails)`),
    "healthSector.publicHealthSector.laboratory.isSecondGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.laboratory.isSecondGuidelineReceived === null`),
    "healthSector.publicHealthSector.laboratory.secondGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.laboratory.secondGuidelineDetails)`),
    // "healthSector.publicHealthSector.publicReportingDetails.isThirdGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['publicReportingDetails']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.publicReportingDetails.isThirdGuidelineReceived === null`),
    // "healthSector.publicHealthSector.publicReportingDetails.thirdGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['publicReportingDetails']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.publicReportingDetails.thirdGuidelineDetails)`),
    "healthSector.publicHealthSector.healthFacility.isThirdGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.healthFacility.isThirdGuidelineReceived === null`),
    "healthSector.publicHealthSector.healthFacility.thirdGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.healthFacility.thirdGuidelineDetails)`),
    "healthSector.publicHealthSector.hospital.isThirdGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.hospital.isThirdGuidelineReceived === null`),
    "healthSector.publicHealthSector.hospital.thirdGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.hospital.thirdGuidelineDetails)`),
    "healthSector.publicHealthSector.laboratory.isThirdGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.laboratory.isThirdGuidelineReceived === null`),
    "healthSector.publicHealthSector.laboratory.thirdGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.laboratory.thirdGuidelineDetails)`),
    //  "healthSector.publicHealthSector.publicReportingDetails.isFourthGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['publicReportingDetails']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.publicReportingDetails.isFourthGuidelineReceived === null`),
    //  "healthSector.publicHealthSector.publicReportingDetails.fourthGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['publicReportingDetails']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.publicReportingDetails.fourthGuidelineDetails)`),
    "healthSector.publicHealthSector.healthFacility.isFourthGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.healthFacility.isFourthGuidelineReceived === null`),
    "healthSector.publicHealthSector.healthFacility.fourthGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.healthFacility.fourthGuidelineDetails)`),
    "healthSector.publicHealthSector.hospital.isFourthGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.hospital.isFourthGuidelineReceived === null`),
    "healthSector.publicHealthSector.hospital.fourthGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.hospital.fourthGuidelineDetails)`),
    "healthSector.publicHealthSector.laboratory.isFourthGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.laboratory.isFourthGuidelineReceived === null`),
    "healthSector.publicHealthSector.laboratory.fourthGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['publicHealthSector']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.publicHealthSector.laboratory.fourthGuidelineDetails)`),

    //"healthSector.privateFormal.privateFormal": new ValidationRuleModel(DataType.Object, false),
    "healthSector.privateFormal.healthFacility": new ValidationRuleModel(DataType.Object, false),
    "healthSector.privateFormal.hospital": new ValidationRuleModel(DataType.Object, false),
    "healthSector.privateFormal.laboratory": new ValidationRuleModel(DataType.Object, false),
    "healthSector.privateFormal.faithBasedClinic": new ValidationRuleModel(DataType.Object, false),
    "healthSector.privateFormal.ngoClinic": new ValidationRuleModel(DataType.Object, false),
    "healthSector.privateFormal.military": new ValidationRuleModel(DataType.Object, false),
    "healthSector.privateFormal.police": new ValidationRuleModel(DataType.Object, false),
    "healthSector.privateFormal.prison": new ValidationRuleModel(DataType.Object, false),
    // "healthSector.privateFormal.privateFormal.isFirstGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['privateFormal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.privateFormal.isFirstGuidelineReceived === undefined`),
    // "healthSector.privateFormal.privateFormal.firstGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['privateFormal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.privateFormal.firstGuidelineDetails)`),
    "healthSector.privateFormal.healthFacility.isFirstGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.healthFacility.isFirstGuidelineReceived === undefined`),
    "healthSector.privateFormal.healthFacility.firstGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.healthFacility.isFirstGuidelineReceived !== null && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.healthFacility.firstGuidelineDetails)`),
    "healthSector.privateFormal.hospital.isFirstGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.hospital.isFirstGuidelineReceived === undefined`),
    "healthSector.privateFormal.hospital.firstGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.hospital.isFirstGuidelineReceived !== null && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.hospital.firstGuidelineDetails)`),
    "healthSector.privateFormal.laboratory.isFirstGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.laboratory.isFirstGuidelineReceived === undefined`),
    "healthSector.privateFormal.laboratory.firstGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.laboratory.isFirstGuidelineReceived !== null && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.laboratory.firstGuidelineDetails)`),
    "healthSector.privateFormal.faithBasedClinic.isFirstGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['faithBasedClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.faithBasedClinic.isFirstGuidelineReceived === undefined`),
    "healthSector.privateFormal.faithBasedClinic.firstGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['faithBasedClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.faithBasedClinic.isFirstGuidelineReceived !== null && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.faithBasedClinic.firstGuidelineDetails)`),
    "healthSector.privateFormal.ngoClinic.isFirstGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['ngoClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.ngoClinic.isFirstGuidelineReceived === undefined`),
    "healthSector.privateFormal.ngoClinic.firstGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['ngoClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.ngoClinic.isFirstGuidelineReceived !== null && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.ngoClinic.firstGuidelineDetails)`),
    "healthSector.privateFormal.military.isFirstGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['military']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.military.isFirstGuidelineReceived === undefined`),
    "healthSector.privateFormal.military.firstGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['military']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.military.isFirstGuidelineReceived !== null && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.military.firstGuidelineDetails)`),
    "healthSector.privateFormal.police.isFirstGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['police']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.police.isFirstGuidelineReceived === undefined`),
    "healthSector.privateFormal.police.firstGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['police']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.police.isFirstGuidelineReceived !== null && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.police.firstGuidelineDetails)`),
    "healthSector.privateFormal.prison.isFirstGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['prison']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.prison.isFirstGuidelineReceived === undefined`),
    "healthSector.privateFormal.prison.firstGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['prison']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.prison.isFirstGuidelineReceived !== null && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.prison.firstGuidelineDetails)`),
    // "healthSector.privateFormal.privateFormal.isSecondGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['privateFormal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.privateFormal.isSecondGuidelineReceived === null`),
    // "healthSector.privateFormal.privateFormal.secondGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['privateFormal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.privateFormal.secondGuidelineDetails)`),
    "healthSector.privateFormal.healthFacility.isSecondGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.healthFacility.isSecondGuidelineReceived === null`),
    "healthSector.privateFormal.healthFacility.secondGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.healthFacility.secondGuidelineDetails)`),
    "healthSector.privateFormal.hospital.isSecondGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.hospital.isSecondGuidelineReceived === null`),
    "healthSector.privateFormal.hospital.secondGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.hospital.secondGuidelineDetails)`),
    "healthSector.privateFormal.laboratory.isSecondGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.laboratory.isSecondGuidelineReceived === null`),
    "healthSector.privateFormal.laboratory.secondGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.laboratory.secondGuidelineDetails)`),
    "healthSector.privateFormal.faithBasedClinic.isSecondGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['faithBasedClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.faithBasedClinic.isSecondGuidelineReceived === null`),
    "healthSector.privateFormal.faithBasedClinic.secondGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['faithBasedClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.faithBasedClinic.secondGuidelineDetails)`),
    "healthSector.privateFormal.ngoClinic.isSecondGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['ngoClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.ngoClinic.isSecondGuidelineReceived === null`),
    "healthSector.privateFormal.ngoClinic.secondGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['ngoClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.ngoClinic.secondGuidelineDetails)`),
    "healthSector.privateFormal.military.isSecondGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['military']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.military.isSecondGuidelineReceived === null`),
    "healthSector.privateFormal.military.secondGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['military']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.military.secondGuidelineDetails)`),
    "healthSector.privateFormal.police.isSecondGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['police']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.police.isSecondGuidelineReceived === null`),
    "healthSector.privateFormal.police.secondGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['police']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.police.secondGuidelineDetails)`),
    "healthSector.privateFormal.prison.isSecondGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['prison']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.prison.isSecondGuidelineReceived === null`),
    "healthSector.privateFormal.prison.secondGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['prison']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.prison.secondGuidelineDetails)`),
    // "healthSector.privateFormal.privateFormal.isThirdGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['privateFormal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.privateFormal.isThirdGuidelineReceived === null`),
    // "healthSector.privateFormal.privateFormal.thirdGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['privateFormal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.privateFormal.thirdGuidelineDetails)`),
    "healthSector.privateFormal.healthFacility.isThirdGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.healthFacility.isThirdGuidelineReceived === null`),
    "healthSector.privateFormal.healthFacility.thirdGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.healthFacility.thirdGuidelineDetails)`),
    "healthSector.privateFormal.hospital.isThirdGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.hospital.isThirdGuidelineReceived === null`),
    "healthSector.privateFormal.hospital.thirdGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.hospital.thirdGuidelineDetails)`),
    "healthSector.privateFormal.laboratory.isThirdGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.laboratory.isThirdGuidelineReceived === null`),
    "healthSector.privateFormal.laboratory.thirdGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.laboratory.thirdGuidelineDetails)`),
    "healthSector.privateFormal.faithBasedClinic.isThirdGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['faithBasedClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.faithBasedClinic.isThirdGuidelineReceived === null`),
    "healthSector.privateFormal.faithBasedClinic.thirdGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['faithBasedClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.faithBasedClinic.thirdGuidelineDetails)`),
    "healthSector.privateFormal.ngoClinic.isThirdGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['ngoClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.ngoClinic.isThirdGuidelineReceived === null`),
    "healthSector.privateFormal.ngoClinic.thirdGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['ngoClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.ngoClinic.thirdGuidelineDetails)`),
    "healthSector.privateFormal.military.isThirdGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['military']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.military.isThirdGuidelineReceived === null`),
    "healthSector.privateFormal.military.thirdGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['military']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.military.thirdGuidelineDetails)`),
    "healthSector.privateFormal.police.isThirdGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['police']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.police.isThirdGuidelineReceived === null`),
    "healthSector.privateFormal.police.thirdGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['police']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.police.thirdGuidelineDetails)`),
    "healthSector.privateFormal.prison.isThirdGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['prison']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.prison.isThirdGuidelineReceived === null`),
    "healthSector.privateFormal.prison.thirdGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['prison']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.prison.thirdGuidelineDetails)`),
    //"healthSector.privateFormal.privateFormal.isFourthGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['privateFormal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.privateFormal.isFourthGuidelineReceived === null`),
    //"healthSector.privateFormal.privateFormal.fourthGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['privateFormal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.privateFormal.fourthGuidelineDetails)`),
    "healthSector.privateFormal.healthFacility.isFourthGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.healthFacility.isFourthGuidelineReceived === null`),
    "healthSector.privateFormal.healthFacility.fourthGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['healthFacility']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.healthFacility.fourthGuidelineDetails)`),
    "healthSector.privateFormal.hospital.isFourthGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.hospital.isFourthGuidelineReceived === null`),
    "healthSector.privateFormal.hospital.fourthGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['hospital']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.hospital.fourthGuidelineDetails)`),
    "healthSector.privateFormal.laboratory.isFourthGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.laboratory.isFourthGuidelineReceived === null`),
    "healthSector.privateFormal.laboratory.fourthGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['laboratory']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.laboratory.fourthGuidelineDetails)`),
    "healthSector.privateFormal.faithBasedClinic.isFourthGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['faithBasedClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.faithBasedClinic.isFourthGuidelineReceived === null`),
    "healthSector.privateFormal.faithBasedClinic.fourthGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['faithBasedClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.faithBasedClinic.fourthGuidelineDetails)`),
    "healthSector.privateFormal.ngoClinic.isFourthGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['ngoClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.ngoClinic.isFourthGuidelineReceived === null`),
    "healthSector.privateFormal.ngoClinic.fourthGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['ngoClinic']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.ngoClinic.fourthGuidelineDetails)`),
    "healthSector.privateFormal.military.isFourthGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['military']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.military.isFourthGuidelineReceived === null`),
    "healthSector.privateFormal.military.fourthGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['military']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.military.fourthGuidelineDetails)`),
    "healthSector.privateFormal.police.isFourthGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['police']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.police.isFourthGuidelineReceived === null`),
    "healthSector.privateFormal.police.fourthGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['police']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.police.fourthGuidelineDetails)`),
    "healthSector.privateFormal.prison.isFourthGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['prison']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.prison.isFourthGuidelineReceived === null`),
    "healthSector.privateFormal.prison.fourthGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateFormal']['prison']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateFormal.prison.fourthGuidelineDetails)`),

    "healthSector.privateInformal.isFirstGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateInformal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateInformal.isFirstGuidelineReceived === undefined`),
    "healthSector.privateInformal.firstGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateInformal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateInformal.isFirstGuidelineReceived !== null && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateInformal.firstGuidelineDetails)`),
    "healthSector.privateInformal.isSecondGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateInformal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateInformal.isSecondGuidelineReceived === null`),
    "healthSector.privateInformal.secondGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateInformal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateInformal.secondGuidelineDetails)`),
    "healthSector.privateInformal.isThirdGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateInformal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateInformal.isThirdGuidelineReceived === null`),
    "healthSector.privateInformal.thirdGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateInformal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateInformal.thirdGuidelineDetails)`),
    "healthSector.privateInformal.isFourthGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateInformal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.privateInformal.isFourthGuidelineReceived === null`),
    "healthSector.privateInformal.fourthGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['privateInformal']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.privateInformal.fourthGuidelineDetails)`),

    "healthSector.community.isFirstGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['community']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.community.isFirstGuidelineReceived === undefined`),
    "healthSector.community.firstGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['community']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline1.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.community.isFirstGuidelineReceived !== null && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.community.firstGuidelineDetails)`),
    "healthSector.community.isSecondGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['community']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.community.isSecondGuidelineReceived === null`),
    "healthSector.community.secondGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['community']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline2.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.community.secondGuidelineDetails)`),
    "healthSector.community.isThirdGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['community']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.community.isThirdGuidelineReceived === null`),
    "healthSector.community.thirdGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['community']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline3.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.community.thirdGuidelineDetails)`),
    "healthSector.community.isFourthGuidelineReceived": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['community']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && ${Constants.Common.RootObjectNameSubstitute}.healthSector.community.isFourthGuidelineReceived === null`),
    "healthSector.community.fourthGuidelineDetails": new ValidationRuleModel(DataType.Boolean, false, `${Constants.Common.RootObjectNameSubstitute}['parentData']['healthSector']['community']['reportingMalaria'] === true && !isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.guideline4.guideLineName) && isStringNullOrEmpty(${Constants.Common.RootObjectNameSubstitute}.healthSector.community.fourthGuidelineDetails)`),
};

export default ValidationRules;

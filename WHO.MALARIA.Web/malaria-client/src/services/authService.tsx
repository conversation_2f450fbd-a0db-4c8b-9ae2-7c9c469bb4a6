﻿import { Constants } from "../models/Constants";
import { CurrentUserModel } from "../models/ProfileModel";
import { UtilityHelper } from "../utils/UtilityHelper";

/** Handles all responsibilities related to authentication */
export const authService = {
  getCurrentUser: (): CurrentUserModel =>
    UtilityHelper.getCookieValue(Constants.Common.UserInfoCookieName)
      ? JSON.parse(
          UtilityHelper.Decrypt(
            UtilityHelper.getCookieValue(Constants.Common.UserInfoCookieName)
          )
        )
      : CurrentUserModel.init(),

  /**
   * force to logout
   */
  logout: () => {
    sessionStorage.clear();

    //Tracking log out event in analytics
    UtilityHelper.onEventAnalytics("Logout", "Logout", "Logout");

    window.location.href = `/Account/SignOut`;
  },

  /** Redirects user to the authenticate themselves
   * @param scheme : scheme name ex. Google, Facebook, Azure
   */
  challenge: (scheme: string = "") => {
    const currentUser = authService.getCurrentUser();
    // if user is already authenticated and token expired
    if (currentUser.scheme) {
      window.location.href = `/Account/Challenge?scheme=${currentUser.scheme}&returnUrl=${window.location.href}`;
    } else if (scheme) {
      // in case from login screen selection
      window.location.href = `/Account/Challenge?scheme=${scheme}&returnUrl=/Home/Index`;
    } else {
      window.location.href = "/Account/Login";
    }
  },

  /** Check if user is authenticated */
  isAuthenticated: () => {
    const currentUser = authService.getCurrentUser();
    return currentUser.userId !== null && currentUser.userId !== "";
  },

  /** get tracking id for analytics */
  getTrackingId: () => {
    const currentUser = authService.getCurrentUser();
    return currentUser.trackingId;
  },

  /** Check before rendering private routes */
  canRenderPrivateRoute: () => {
    const currentUser = authService.getCurrentUser();
    const isAuthenticated = authService.isAuthenticated();

    return isAuthenticated || (!isAuthenticated && currentUser.isNewUser);
  },

  /** Check if current user is deactivated */
  isUserDeactivated: () => {
    const currentUser = authService.getCurrentUser();
    return currentUser.isDeactivated === true;
  },

  /** Check if current user is inactive */
  isUserInactive: () => {
    const currentUser = authService.getCurrentUser();
    return currentUser.isActive === false;
  },
};

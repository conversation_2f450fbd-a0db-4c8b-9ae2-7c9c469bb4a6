using MediatR;
using Microsoft.AspNetCore.Authentication;
using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Identity;
using Microsoft.AspNetCore.Mvc;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Queries;

namespace WHO.MALARIA.Web.Controllers
{
    /// <summary>
    /// Simplified authentication controller without Identity Server
    /// </summary>
    public class AccountController : Controller
    {
        private readonly IMediator _mediator;
        private readonly SignInManager<IdentityDto> _signInManager;
        private readonly AppSettings _appSettings;

        public AccountController(
            IMediator mediator,
            SignInManager<IdentityDto> signInManager,
            AppSettings appSettings)
        {
            _mediator = mediator;
            _signInManager = signInManager;
            _appSettings = appSettings;
        }

        /// <summary>
        /// Login page - redirects to Azure AD
        /// </summary>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult Login(string returnUrl = null)
        {
            if (User.Identity.IsAuthenticated)
            {
                return Redirect(returnUrl ?? "/");
            }

            return Challenge(new AuthenticationProperties
            {
                RedirectUri = Url.Action("Callback", new { returnUrl })
            }, Constants.IdentityConstant.AzureActiveDirectory);
        }

        /// <summary>
        /// Challenge external authentication provider
        /// </summary>
        [HttpGet]
        [AllowAnonymous]
        public IActionResult Challenge(string scheme = null, string returnUrl = null)
        {
            if (string.IsNullOrEmpty(scheme))
            {
                scheme = Constants.IdentityConstant.AzureActiveDirectory;
            }

            if (string.IsNullOrEmpty(returnUrl))
            {
                returnUrl = "/";
            }

            var props = new AuthenticationProperties
            {
                RedirectUri = Url.Action("Callback", new { returnUrl }),
                Items =
                {
                    { "returnUrl", returnUrl },
                    { "scheme", scheme }
                }
            };

            return Challenge(props, scheme);
        }

        /// <summary>
        /// Handle authentication callback from Azure AD
        /// </summary>
        [HttpGet]
        [AllowAnonymous]
        public async Task<IActionResult> Callback(string returnUrl = null)
        {
            var result = await HttpContext.AuthenticateAsync(Constants.IdentityConstant.AzureActiveDirectory);

            if (result?.Succeeded != true)
            {
                return RedirectToAction("Error");
            }

            returnUrl = returnUrl ?? "/";

            // Extract user information from claims
            var claims = result.Principal.Claims;
            string email = claims.FirstOrDefault(x => x.Type == ClaimTypes.Email)?.Value;
            string name = claims.FirstOrDefault(x => x.Type == ClaimTypes.Name)?.Value;
            string providerUserId = claims.FirstOrDefault(x => x.Type == ClaimTypes.NameIdentifier)?.Value;

            if (string.IsNullOrEmpty(email))
            {
                return RedirectToAction("Error");
            }

            bool isInternalWHOUser = email.EndsWith("@who.int", StringComparison.OrdinalIgnoreCase);

            // Find or create user
            IdentityDto identityDto = await FindOrCreateUser(email, name, providerUserId, isInternalWHOUser);

            if (identityDto == null)
            {
                return RedirectToAction("Error");
            }

            // Sign in the user
            await _signInManager.SignInAsync(identityDto, false, Constants.IdentityConstant.AzureActiveDirectory);

            // Handle user status checks
            if (!identityDto.Status && isInternalWHOUser)
            {
                await SignOutCurrentUser();
                return Redirect("/deactivatedwhouser");
            }
            else if (!identityDto.Status && !identityDto.IsDeactivated)
            {
                identityDto.IsDeactivated = true;
                CreateCookie(identityDto);
                return Redirect("/");
            }
            else if (!identityDto.Status && identityDto.IsDeactivated)
            {
                CreateCookie(identityDto);
                return Redirect("/");
            }
            else
            {
                var hasUserActiveForAssignedCountriesCommand = new HasUserActiveForAssignedCountriesCommand(identityDto.User.Id);
                bool hasUserActiveForAssignedCountries = await _mediator.Send(hasUserActiveForAssignedCountriesCommand);

                if (!hasUserActiveForAssignedCountries && !isInternalWHOUser && identityDto.User.UserType != (int)UserRoleEnum.WHOAdmin)
                {
                    CreateCookie(identityDto);
                    return Redirect("/inActivatedUser");
                }
            }

            // Handle WHO users without country access
            if (isInternalWHOUser && identityDto.User.UserType == 0)
            {
                identityDto.Name = identityDto?.Name ?? identityDto.Username;
                CreateCookie(identityDto);
                return Redirect("/countryaccessrequest");
            }

            // Create cookie for successful active users
            CreateCookie(identityDto);

            return Redirect(returnUrl);
        }

        /// <summary>
        /// Logout user
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> Logout()
        {
            await SignOutCurrentUser();
            return Redirect("/");
        }

        /// <summary>
        /// Sign out endpoint for frontend
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> SignOut()
        {
            DeleteAllCookies();
            return SignOut(CookieAuthenticationDefaults.AuthenticationScheme, Constants.IdentityConstant.AzureActiveDirectory);
        }

        /// <summary>
        /// Access denied page
        /// </summary>
        [HttpGet]
        public IActionResult AccessDenied()
        {
            return View();
        }

        /// <summary>
        /// Error page
        /// </summary>
        [HttpGet]
        public IActionResult Error()
        {
            return View();
        }

        #region Private Methods

        private async Task<IdentityDto> FindOrCreateUser(string email, string name, string providerUserId, bool isInternalWHOUser)
        {
            try
            {
                // Find existing user by email
                var filterCriteria = new List<FilterCriteria>
                {
                    new FilterCriteria { Field = "Email", Operator = Constants.DbOperators.Equals, Value = email }
                };

                var identities = await _mediator.Send(new GetIdentityQuery(filterCriteria));
                var identity = identities.FirstOrDefault();

                if (identity != null)
                {
                    return identity;
                }

                // For WHO internal users, create automatically
                if (isInternalWHOUser)
                {
                    // Create new WHO user automatically
                    var createCommand = new CreateIdentityCommand
                    {
                        Email = email,
                        Name = name ?? email,
                        OrganizationName = Constants.IdentityConstant.WhoOrganizationName,
                        UserType = UserRoleEnum.WHOViewer,
                        CountryRequestedForIds = new List<Guid>(), // Empty for WHO users initially
                        IsRequestCameFromRegisterUserScreen = false
                    };

                    return await _mediator.Send(createCommand);
                }

                // For external users, redirect to registration
                return null;
            }
            catch (Exception)
            {
                return null;
            }
        }

        private async Task SignOutCurrentUser()
        {
            if (User.Identity.IsAuthenticated)
            {
                await HttpContext.SignOutAsync(CookieAuthenticationDefaults.AuthenticationScheme);
                await _signInManager.SignOutAsync();
                DeleteAllCookies();
            }
        }

        private void CreateCookie(IdentityDto identity)
        {
            var userInfoStringify = JsonConvert.SerializeObject(new
            {
                userId = identity.User.Id,
                identityId = identity.Id,
                isNewUser = false,
                name = identity.User.Name,
                email = identity.Email,
                userType = identity.User.UserType,
                isDeactivated = identity.IsDeactivated,
                isActive = identity.Status,
                scheme = Constants.IdentityConstant.AzureActiveDirectory,
                trackingId = _appSettings.AnalyticsFields.TrackingId
            });

            HttpContext.Response.Cookies.Append(Constants.Common.UserInfoCookieName, Crypto.Encrypt(userInfoStringify));
        }

        private void DeleteAllCookies()
        {
            foreach (var cookie in Request.Cookies.Keys)
            {
                Response.Cookies.Delete(cookie);
            }
        }

        #endregion
    }
}

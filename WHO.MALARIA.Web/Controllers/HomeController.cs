
using MediatR;
using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Newtonsoft.Json;
using System;
using System.Collections.Generic;
using System.Diagnostics;
using System.Linq;
using System.Security.Claims;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Queries;
using WHO.MALARIA.Features;
using WHO.MALARIA.Web.Models;

namespace WHO.MALARIA.Web.Controllers
{
    [Route("[controller]/[action]")]
    public class HomeController : BaseController
    {
        private readonly ILogger<HomeController> _logger;
        private readonly AppSettings _appSettings;
        private readonly IMediator _mediator;

        public HomeController(ILogger<HomeController> logger, AppSettings appSettings, IMediator mediator)
        {
            _appSettings = appSettings;
            _logger = logger;
            _mediator = mediator;
        }

        [Authorize]
        [HttpGet]
        public async Task<IActionResult> Index()
        {
            await CreateCurrentUserCookies();
            return Redirect("/Dashboard");
        }

        [HttpGet]
        public IActionResult Privacy()
        {
            return View();
        }

        [HttpGet]
        [ResponseCache(Duration = 0, Location = ResponseCacheLocation.None, NoStore = true)]
        public IActionResult Error()
        {
            return View(new ErrorViewModel { RequestId = Activity.Current?.Id ?? HttpContext.TraceIdentifier });
        }

        [HttpGet]
        [Authorize]
        public IActionResult DebugClaims()
        {
            var claims = HttpContext.User.Claims.Select(c => new { Type = c.Type, Value = c.Value }).ToList();
            return Json(claims);
        }

        /// <summary>
        /// If email address exists in database then not a new user otherwise create a cookie to be used in client side
        /// </summary>
        private async Task CreateCurrentUserCookies()
        {
            // Use standard claim types for Azure AD OpenID Connect
            Claim email = HttpContext.User.Claims.FirstOrDefault(x => string.Equals(x.Type, ClaimTypes.Email, System.StringComparison.InvariantCultureIgnoreCase));
            Claim name = HttpContext.User.Claims.FirstOrDefault(x => string.Equals(x.Type, ClaimTypes.Name, System.StringComparison.InvariantCultureIgnoreCase));

            // Fallback to alternative claim types if standard ones are not found
            if (email == null)
            {
                email = HttpContext.User.Claims.FirstOrDefault(x => string.Equals(x.Type, Constants.IdentityClaims.Email, System.StringComparison.InvariantCultureIgnoreCase));
            }

            if (name == null)
            {
                name = HttpContext.User.Claims.FirstOrDefault(x => string.Equals(x.Type, Constants.IdentityClaims.Name, System.StringComparison.InvariantCultureIgnoreCase));
            }

            if (!string.IsNullOrWhiteSpace(email?.Value))
            {
                // check if user is already registered with email
                IEnumerable<IdentityDto> identities = await _mediator.Send(
                    new GetIdentityQuery(
                      new List<FilterCriteria> {
                      new FilterCriteria{ Field="Email", Operator=Constants.DbOperators.Equals, Value = email?.Value }
                  }));

                _ = CreateCookie(name?.Value, email?.Value, identities);
            }
            else
            {
                _logger.LogWarning("No email claim found for authenticated user. Available claims: {Claims}",
                    string.Join(", ", HttpContext.User.Claims.Select(c => $"{c.Type}={c.Value}")));
            }
        }

        /// <summary>
        /// Create a cookie which will later be used by client application
        /// </summary>
        /// <param name="name">String value, Name of the user logged in</param>
        /// <param name="email">String value, Email of the user loggied in</param>
        /// <param name="identities">List of IdentityDto objects</param>
        /// <returns></returns>
        private async Task CreateCookie(string name, string email, IEnumerable<IdentityDto> identities)
        {
            int? userType = null;
            Guid? identityId = null;
            Guid? userId = null;
            string scheme = HttpContext.User.FindFirstValue("idp");

            if (identities.Any())
            {
                IdentityDto identity = identities.First();
                userType = identity.User?.UserType;
                userId = identity.User?.Id;
                identityId = identity?.Id;
            }

            bool isNewUser = !identities.Any();

            string trackingId = _appSettings.AnalyticsFields.TrackingId;

            string userInfoStringify = JsonConvert.SerializeObject(new
            {
                userId,
                identityId,
                isNewUser,
                name,
                email,
                userType,
                scheme,
                trackingId
            });

            HttpContext.Response.Cookies.Append(Constants.Common.UserInfoCookieName, Crypto.Encrypt(userInfoStringify));
        }
    }
}

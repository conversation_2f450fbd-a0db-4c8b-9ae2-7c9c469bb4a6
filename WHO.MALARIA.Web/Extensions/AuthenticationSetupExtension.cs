using Microsoft.AspNetCore.Authentication.Cookies;
using Microsoft.AspNetCore.Authentication.OpenIdConnect;
using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Configuration;
using Microsoft.Extensions.DependencyInjection;
using Microsoft.IdentityModel.Tokens;
using System;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Models;

using Microsoft.AspNetCore.Identity;
using WHO.MALARIA.Web.Infrastructure.Events;

namespace WHO.MALARIA.Web.Extensions
{
    /// <summary>
    /// Simplified authentication setup without Identity Server
    /// </summary>
    internal static class AuthenticationSetupExtension
    {
        /// <summary>
        /// Configure authentication services with direct Azure AD integration
        /// </summary>
        /// <param name="services">Service collection</param>
        /// <param name="appSettings">Application settings</param>
        /// <returns>Service collection</returns>
        internal static IServiceCollection AddSimplifiedAuthentication(this IServiceCollection services, AppSettings appSettings)
        {
            // Custom claims factory to populate UserId
            services.AddScoped<IUserClaimsPrincipalFactory<IdentityDto>, CustomClaimsPrincipalFactory>();

            // Configure cookie policy
            services.Configure<CookiePolicyOptions>(options =>
            {
                options.CheckConsentNeeded = context => true;
                options.MinimumSameSitePolicy = SameSiteMode.Strict;
            });

            // Configure authentication
            services.AddAuthentication(options =>
            {
                options.DefaultAuthenticateScheme = CookieAuthenticationDefaults.AuthenticationScheme;
                options.DefaultSignInScheme = CookieAuthenticationDefaults.AuthenticationScheme;
                options.DefaultChallengeScheme = Constants.IdentityConstant.AzureActiveDirectory;
            })
            .AddCookie(CookieAuthenticationDefaults.AuthenticationScheme, options =>
            {
                options.Cookie.SameSite = SameSiteMode.None; // must be None for cross-site
                options.Cookie.HttpOnly = true;
                options.Cookie.SecurePolicy = Microsoft.AspNetCore.Http.CookieSecurePolicy.SameAsRequest;
                options.ExpireTimeSpan = TimeSpan.FromHours(12);
                options.SlidingExpiration = true;
                options.LoginPath = "/Account/Login";
                options.LogoutPath = "/Account/Logout";
                options.AccessDeniedPath = "/Account/AccessDenied";
                options.EventsType = typeof(CustomCookieAuthenticationEvents);
            })
            .AddOpenIdConnect(Constants.IdentityConstant.AzureActiveDirectory, options =>
            {
                options.SignInScheme = CookieAuthenticationDefaults.AuthenticationScheme;
                options.SignOutScheme = CookieAuthenticationDefaults.AuthenticationScheme;

                options.Authority = $"{Constants.IdentityConstant.AzureActiveDirectoryAuthority}{appSettings.AzureAD.TenantId}";
                options.ClientId = appSettings.AzureAD.ClientId;
                options.ClientSecret = appSettings.AzureAD.ClientSecret;

                options.ResponseType = "code";

                // Use configured base URL for Azure AD redirect URIs
                string azureBaseUrl = appSettings.AzureAD.BaseUrl ?? "https://localhost:5001";

                options.CallbackPath = "/signin-aad";
                options.SignedOutCallbackPath = "/signout-callback-aad";
                options.RemoteSignOutPath = "/signout-aad";

                options.Resource = Constants.IdentityConstant.AzureActiveDirectoryResource;

                options.TokenValidationParameters = new TokenValidationParameters
                {
                    NameClaimType = "name",
                    RoleClaimType = "role"
                };

                options.SaveTokens = true;

                // Configure scopes
                options.Scope.Clear();
                options.Scope.Add("openid");
                options.Scope.Add("profile");
                options.Scope.Add("email");

                // Handle events
                options.Events = new OpenIdConnectEvents
                {
                    OnRedirectToIdentityProvider = ctx =>
                    {
                        var request = ctx.Request;
                        ctx.ProtocolMessage.RedirectUri = $"{appSettings.AzureAD.BaseUrl}{ctx.Options.CallbackPath}";
                        ctx.ProtocolMessage.IssuerAddress = ctx.ProtocolMessage.IssuerAddress?.Replace(
                            request.Scheme + "://" + request.Host.Value,
                            appSettings.AzureAD.BaseUrl);
                        return Task.CompletedTask;
                    },
                    OnAuthenticationFailed = ctx =>
                    {
                        ctx.Response.Redirect("/Account/Error");
                        ctx.HandleResponse();
                        return Task.CompletedTask;
                    }
                };
            });

            return services;
        }
    }
}

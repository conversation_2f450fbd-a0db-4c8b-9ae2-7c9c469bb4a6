using Microsoft.AspNetCore.Identity;
using System;
using System.Collections.Generic;
using System.Linq;
using System.Security.Claims;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Domain.Dtos;

namespace WHO.MALARIA.Web.Infrastructure.Identity
{
    /// <summary>
    /// Custom UserStore implementation for IdentityDto
    /// This is a minimal implementation required for ASP.NET Core Identity to work
    /// </summary>
    public class UserStore : IUserStore<IdentityDto>, IUserPasswordStore<IdentityDto>, IUserClaimsStore<IdentityDto>
    {
        public Task<IdentityResult> CreateAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            // This application doesn't create users through Identity - users are created through MediatR commands
            return Task.FromResult(IdentityResult.Success);
        }

        public Task<IdentityResult> DeleteAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            // This application doesn't delete users through Identity
            return Task.FromResult(IdentityResult.Success);
        }

        public void Dispose()
        {
            // Nothing to dispose
        }

        public Task<IdentityDto> FindByIdAsync(string userId, CancellationToken cancellationToken)
        {
            // This method is not used in the simplified authentication flow
            return Task.FromResult<IdentityDto>(null);
        }

        public Task<IdentityDto> FindByNameAsync(string normalizedUserName, CancellationToken cancellationToken)
        {
            // This method is not used in the simplified authentication flow
            return Task.FromResult<IdentityDto>(null);
        }

        public Task<string> GetNormalizedUserNameAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.Email?.ToUpperInvariant());
        }

        public Task<string> GetUserIdAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.Id.ToString());
        }

        public Task<string> GetUserNameAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            return Task.FromResult(user.Email);
        }

        public Task SetNormalizedUserNameAsync(IdentityDto user, string normalizedName, CancellationToken cancellationToken)
        {
            // Not needed for this implementation
            return Task.CompletedTask;
        }

        public Task SetUserNameAsync(IdentityDto user, string userName, CancellationToken cancellationToken)
        {
            user.Email = userName;
            return Task.CompletedTask;
        }

        public Task<IdentityResult> UpdateAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            // This application doesn't update users through Identity
            return Task.FromResult(IdentityResult.Success);
        }

        // IUserPasswordStore implementation
        public Task<string> GetPasswordHashAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            // This application uses external authentication, no password storage
            return Task.FromResult<string>(null);
        }

        public Task<bool> HasPasswordAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            // This application uses external authentication, no passwords
            return Task.FromResult(false);
        }

        public Task SetPasswordHashAsync(IdentityDto user, string passwordHash, CancellationToken cancellationToken)
        {
            // This application uses external authentication, no password storage
            return Task.CompletedTask;
        }

        // IUserClaimsStore implementation
        public Task AddClaimsAsync(IdentityDto user, IEnumerable<Claim> claims, CancellationToken cancellationToken)
        {
            // Claims are handled by CustomClaimsPrincipalFactory
            return Task.CompletedTask;
        }

        public Task<IList<Claim>> GetClaimsAsync(IdentityDto user, CancellationToken cancellationToken)
        {
            // Claims are handled by CustomClaimsPrincipalFactory
            return Task.FromResult<IList<Claim>>(new List<Claim>());
        }

        public Task<IList<IdentityDto>> GetUsersForClaimAsync(Claim claim, CancellationToken cancellationToken)
        {
            // Not used in this application
            return Task.FromResult<IList<IdentityDto>>(new List<IdentityDto>());
        }

        public Task RemoveClaimsAsync(IdentityDto user, IEnumerable<Claim> claims, CancellationToken cancellationToken)
        {
            // Claims are handled by CustomClaimsPrincipalFactory
            return Task.CompletedTask;
        }

        public Task ReplaceClaimAsync(IdentityDto user, Claim claim, Claim newClaim, CancellationToken cancellationToken)
        {
            // Claims are handled by CustomClaimsPrincipalFactory
            return Task.CompletedTask;
        }
    }
}

﻿using System;
using System.IO;
using System.Linq;
using System.Threading.Tasks;

using Cloudmersive.APIClient.NETCore.VirusScan.Api;
using Cloudmersive.APIClient.NETCore.VirusScan.Client;
using Cloudmersive.APIClient.NETCore.VirusScan.Model;

using Microsoft.AspNetCore.Builder;
using Microsoft.AspNetCore.Http;

using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Features.Exceptions;
using WHO.MALARIA.Common.Services;

namespace WHO.MALARIA.Web.Middlewares
{
    /// <summary>
    /// Scans the files for vulnerability.
    /// </summary>
    public class VirusScanMiddleware
    {
        private readonly RequestDelegate _next;
        private readonly AppSettings _appSettings;

        public VirusScanMiddleware(RequestDelegate next, AppSettings appSettings)
        {
            _next = next;
            _appSettings = appSettings;
        }

        public async Task Invoke(HttpContext context, IServiceProvider serviceProvider)
        {
            if (context.Request.HasFormContentType && context.Request.Form.Files.Any())
            {
                Configuration.Default.AddApiKey("Apikey", _appSettings.Cloudmersive.ApiKey);
                ScanApi apiInstance = new ScanApi();
                ITranslationService translationService = (ITranslationService)serviceProvider.GetService(typeof(ITranslationService));

                try
                {
                    foreach (IFormFile file in context.Request.Form.Files)
                    {
                        string fileExtension = Path.GetExtension(file.FileName);
                        VirusScanAdvancedResult result = new VirusScanAdvancedResult();

                        result = apiInstance.ScanFileAdvanced(file.OpenReadStream(), restrictFileTypes: Domain.Constants.Constants.Common.AllValidFileExtensions);

                        if (result.CleanResult != true)
                        {
                            Console.WriteLine($"Verified file-type is '{result.VerifiedFileFormat}'");
                            throw new VirusScanException(file.FileName,
                                                            context.Connection.RemoteIpAddress.ToString(),
                                                            result.ContainsExecutable.Value,
                                                            result.ContainsInvalidFile.Value,
                                                            result.ContainsScript.Value,
                                                            result.ContainsPasswordProtectedFile.Value,
                                                            result.ContainsRestrictedFileFormat.Value,
                                                            result.ContainsMacros.Value,
                                                            result.ContainsXmlExternalEntities.Value,
                                                            result.ContainsInsecureDeserialization.Value,
                                                            result.ContainsHtml.Value,
                                                            result.ToJson(),
                                                            translationService.GetTranslatedMessage);
                        }
                    }
                }
                catch (Exception ex)
                {
                    if (ex is ApiException exception)
                        throw new VirusScanFailedException(translationService.GetTranslatedMessage(Domain.Constants.Constants.Exception.VirusScanFailed),
                                                           exception.Message,
                                                           exception.ErrorCode.ToString(),
                                                           exception.ErrorContent,
                                                           exception.StackTrace,
                                                           exception.Source);

                    throw;
                }
            }

            await _next.Invoke(context);
        }
    }

    /// <summary>
    /// Use to add virus scanner in the request/response pipeline of the application
    /// </summary>
    public static class VirusScanMiddlewareExtension
    {
        public static IApplicationBuilder UseVirusScan(this IApplicationBuilder builder) => builder.UseMiddleware<VirusScanMiddleware>();
    }
}

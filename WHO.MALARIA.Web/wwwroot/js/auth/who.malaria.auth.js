﻿$(document).ready(() => {
  onBoxClick();
  onProceed();
  onBack();
});

// mark the box active
onBoxClick = () => {
  $(".who-body").delegate(".who-box, .user-text", "click", (e) => {
    const currentElement = e.target;

    // check if user has clicked on <i> or <div>
    const parentElement =
      currentElement.tagName === "I" || currentElement.tagName === "H6"
        ? $(currentElement).parents("div.who-box")
        : currentElement;

    $(".who-box").removeClass("active");
    $(parentElement).addClass("active");
  });
};

// triggers when user clicks on back button
onBack = () => {
  $("#back").on("click", () => {
    showMainScreen();
  });
};

// triggers when user clicks on 'LETS START' button
onProceed = () => {
  $(".proceed").on("click", () => {
    // find the current active element
    const selectedBox = $(".who-box.active");

    if (selectedBox.length === 0) return;

    const type = selectedBox.data("type");

    // for WHO AD Users redirect to AZURE AD
    if (type === "WHO") {
      window.location.href =
        "/Account/Challenge?scheme=aad&returnUrl=/Home/Index";
    }
    // for external or 'non who' users will show another screen
    else if (type === "External") {
      showExternalAuthenticationWindows();
    }
  });
};

// show the external login screen
showExternalAuthenticationWindows = () => {
  $("#main-screen").hide("slide", { direction: "left" }, 500, function () {
    $("#external-screen").show("slide", { direction: "right" }, 500);
  });
};

// show the main login screen
showMainScreen = () => {
  $("#external-screen").hide("slide", { direction: "left" }, 500, function () {
    $("#main-screen").show("slide", { direction: "right" }, 500);
  });
};

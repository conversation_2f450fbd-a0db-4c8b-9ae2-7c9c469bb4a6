﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Collections.Generic;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Services.Rules;
using WHO.MALARIA.Services.Rules.Identity;
using WHO.MALARIA.Services.Rules.Shared;
using WHO.MALARIA.Services.Rules.User;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Adds existing WHO users into the system only when they are existed in the WHO Azure Active Directory
    /// </summary>
    public class AddExistingUserCommandHandler : RuleBase, IRequestHandler<AddExistingUserCommand, bool>
    {
        private readonly IMediator _mediator;
        private readonly IUnitOfWork _unitOfWork;
        private readonly IIdentityRuleChecker _identityRuleChecker;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IUserRuleChecker _userRuleChecker;
        private readonly ITranslationService _translationService;

        public AddExistingUserCommandHandler(IMediator mediator,
          IUnitOfWork unitOfWork,
          IIdentityRuleChecker identityChecker,
          IUserRuleChecker userRuleChecker,
          ICommonRuleChecker commonRuleChecker,
          ITranslationService translationService)
        {
            _mediator = mediator;
            _unitOfWork = unitOfWork;
            _identityRuleChecker = identityChecker;
            _commonRuleChecker = commonRuleChecker;
            _userRuleChecker = userRuleChecker;
            _translationService = translationService;         
        }

        /// <summary>
        /// Performs validations, adds identity for the user and creates a new user which is existed in the WHO Azure AD
        /// </summary>
        /// <param name="request">Instance of AddExistingUserCommand class that contains the request parameters</param>
        /// <param name="cancellationToken">CancellationToken token to cancel current task</param>
        /// <returns>True when request is user created successfully or validation message if any</returns>
        public async Task<bool> Handle(AddExistingUserCommand request, CancellationToken cancellationToken)
        {
            // Check Business Rules
            CheckRule(new ValidEmailRule(_translationService, _identityRuleChecker, request.Email));
            CheckRule(new IdentityEmailMustbeUniqueRule(_translationService, _identityRuleChecker, request.Email));
            CheckRule(new UserShouldBeWhoAdminRule(_translationService, _userRuleChecker, request.CurrentUserId));
            CheckRule(new IsEnumValueValidRule(_translationService, typeof(UserRoleEnum), Convert.ToInt32(request.Role), nameof(request.Role)));


            User currentUser = await _unitOfWork.UserRepository.Queryable(u => u.Id == request.CurrentUserId)
                                                               .Include(u => u.Identity)
                                                               .SingleAsync();
            CheckRule(new UserShouldBeInWhoActiveDirectoryRule(_translationService, _userRuleChecker, currentUser.Identity.Username, request.Email));

            // create identity
            Identity identity = new Identity(request.Email, request.Email, Constants.IdentityConstant.Internal, true);
            _unitOfWork.IdentityRepository.Add(identity);

            // create user
            User user = new User(identity.Id, request.Name, Constants.IdentityConstant.WhoOrganizationName, (int)UserStatus.Active, true);
            _unitOfWork.UserRepository.Add(user);

            if (request.Role == UserRoleEnum.SuperManager)
            {
                CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.CountryId.Value, nameof(request.CountryId)));
                CheckRule(new SingleSuperManagerPerCountryRule(_translationService, _userRuleChecker, request.CountryId.Value));

                user.IsWhoAdmin = false;

                // User Country Access Mapping
                UserCountryAccess userCountryAccess = new UserCountryAccess(user.Id, request.CountryId.Value, (int)UserRoleEnum.SuperManager, (int)UserCountryAccessRightsEnum.Accepted);

                _unitOfWork.UserCountryAccessRepository.Add(userCountryAccess);
            }

            else if (request.Role == UserRoleEnum.WHOAdmin)
            {
                IEnumerable<Country> countries = await _unitOfWork.CountryRepository.GetListAsync(country => country.IsActive);

                List<UserCountryAccess> userCountryAccesses = new List<UserCountryAccess>();

                foreach (Country country in countries)
                {
                    userCountryAccesses.Add(new UserCountryAccess(user.Id, country.Id, (int)UserRoleEnum.Viewer, (int)UserCountryAccessRightsEnum.Accepted));
                }

                _unitOfWork.UserCountryAccessRepository.AddRange(userCountryAccesses);
            }

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            string countryName = "";
            if (request.CountryId != null)
            {
                Country country = await _unitOfWork.CountryRepository.Queryable(c => c.Id == request.CountryId).SingleAsync();
                countryName = country?.Name;
            }

            // after user creation send notification email   
            await _mediator.Publish(new UserRoleAssignmentEmailNotification(request.Email, countryName, request.Role));

            return true;
        }
    }
}

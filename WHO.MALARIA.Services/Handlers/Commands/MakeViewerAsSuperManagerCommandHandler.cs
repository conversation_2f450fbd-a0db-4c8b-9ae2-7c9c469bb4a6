﻿using MediatR;
using Microsoft.EntityFrameworkCore;
using System;
using System.Linq;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Exceptions;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules.Shared;
using WHO.MALARIA.Services.Rules.User;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Handles the incoming request to make viewer as supermanager
    /// </summary>
    public class MakeViewerAsSuperManagerCommandHandler : RuleBase, IRequestHandler<MakeViewerAsSuperManagerCommand, bool>
    {
        #region Variable Declaration

        private readonly IUnitOfWork _unitOfWork;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IUserRuleChecker _userRuleChecker;
        private readonly ITranslationService _translationService;
        private readonly IMediator _mediator;

        #endregion

        #region Constructor
        public MakeViewerAsSuperManagerCommandHandler(
            IUnitOfWork unitOfWork,
            ICommonRuleChecker commonRuleChecker,
            IUserRuleChecker userRuleChecker,
            ITranslationService translationService,
            IMediator mediator)
        {
            _unitOfWork = unitOfWork;
            _commonRuleChecker = commonRuleChecker;
            _userRuleChecker = userRuleChecker;
            _translationService = translationService;
            _mediator = mediator;
        }
        #endregion

        #region Command Handler
        /// <summary>
        /// Update user type from viewer to super manager by who admin
        /// </summary>
        /// <param name="request">Contains user data to update</param>
        /// <param name="cancellationToken">Notifies to cancel the operation</param>
        /// <returns>TRUE if user type gets updated else throw an error if any validation fails</returns>
        public async Task<bool> Handle(MakeViewerAsSuperManagerCommand request, CancellationToken cancellationToken)
        {
            //Check business rules
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.UserId, nameof(request.UserId)));
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.CountryId, nameof(request.CountryId)));
            CheckRule(new UserShouldBeWhoAdminRule(_translationService, _userRuleChecker, request.CurrentUserId));
            CheckRule(new UserShouldBeViewerRule(_translationService, _userRuleChecker, request.UserId));
            CheckRule(new HasDifferentSuperManagerAssignedToCountryRule(_translationService, _userRuleChecker, request.UserId, request.CountryId));

            User user = await _unitOfWork.UserRepository
                              .Queryable(u => u.Id == request.UserId)
                              .Include(u => u.Identity)
                              .Include(u => u.UserCountryAccesses)
                              .FirstOrDefaultAsync();
            if (user == null)
            {
                throw new RecordNotFoundException(request.UserId, "User");
            }

            UserCountryAccess userCountryAccess = user.UserCountryAccesses.SingleOrDefault(c => c.CountryId == request.CountryId && c.UserId == request.UserId);

            userCountryAccess.UserType = (int)UserRoleEnum.SuperManager;

            bool shouldInvitationEmailBeSent = false;

            if (user.Status == (int)UserStatus.Pending)
            {
                userCountryAccess.Status = (int)UserCountryAccessRightsEnum.InvitationNotAccepted;
                shouldInvitationEmailBeSent = true;
            }

            _unitOfWork.UserCountryAccessRepository.Update(userCountryAccess);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            Country country = await _unitOfWork.CountryRepository.GetAsync(d => d.Id == userCountryAccess.CountryId);

            if (shouldInvitationEmailBeSent)
            {
                User currentUser = await _unitOfWork.UserRepository.Queryable(u => u.Id == request.CurrentUserId)
                                                                   .Include(u => u.Identity)
                                                                   .SingleAsync();

                await _mediator.Publish(new UserInvitationEmailNotification(user.Name, user.Identity.Email, country.Name, currentUser.Identity.Username, userCountryAccess.Id));
            }
            else
            {
                await _mediator.Publish(new UserRoleAssignmentEmailNotification(user.Identity.Email, country.Name, UserRoleEnum.SuperManager));
            }
            return true;
        }
        #endregion
    }
}
﻿using MediatR;
using System;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.Dxos;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Class to handle create identity and user for who user command
    /// </summary>
    public class CreateIdentityWithUserCommandHandler : IRequestHandler<CreateIdentityWithUserCommand, IdentityDto>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IIdentityDxos _identityDxos;
        private readonly IUserDxos _userDxos;

        public CreateIdentityWithUserCommandHandler(IUnitOfWork unitOfWork, IIdentityDxos identityDxos, IUserDxos userDxos)
        {
            _unitOfWork = unitOfWork;
            _identityDxos = identityDxos;
            _userDxos = userDxos;
        }

        public async Task<IdentityDto> Handle(CreateIdentityWithUserCommand request, CancellationToken cancellationToken)
        {
            // create identity
            Identity identity = new Identity(request.Email, request.Email, Constants.IdentityConstant.Internal, true);
            identity.Status = true;
            identity.CreatedAt = DateTime.UtcNow;

            _unitOfWork.IdentityRepository.Add(identity);

            // create user
            User user = new User(identity.Id, request.Email, Constants.IdentityConstant.WhoOrganizationName);

            user.Status = (int)UserStatus.Active;
            user.CreatedAt = DateTime.UtcNow;
            user.CreatedBy = identity.Id;

            _unitOfWork.UserRepository.Add(user);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            IdentityDto identityDto = _identityDxos.MapIdentityDto(identity);
            identityDto.User = _userDxos.MapUserDto(user);

            return identityDto;
        }
    }
}

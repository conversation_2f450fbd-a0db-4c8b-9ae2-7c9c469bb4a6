﻿using MediatR;
using Microsoft.AspNetCore.Http;
using Microsoft.AspNetCore.Identity;
using Microsoft.EntityFrameworkCore;
using System;
using System.Threading;
using System.Threading.Tasks;
using WHO.MALARIA.Database;
using WHO.MALARIA.Domain.Commands;
using WHO.MALARIA.Domain.Constants;
using WHO.MALARIA.Domain.Dtos;
using WHO.MALARIA.Domain.Enum;
using WHO.MALARIA.Domain.Events;
using WHO.MALARIA.Domain.Models;
using WHO.MALARIA.Domain.Models.Identity;
using WHO.MALARIA.Services.BusinessRuleValidations.Interfaces;
using WHO.MALARIA.Services.Dxos;
using WHO.MALARIA.Common.Services;
using WHO.MALARIA.Services.Rules;
using WHO.MALARIA.Services.Rules.Identity;
using WHO.MALARIA.Services.Rules.Shared;
using WHO.MALARIA.Services.Rules.User;

namespace WHO.MALARIA.Services.Handlers.Commands
{
    /// <summary>
    /// Class to handle create new external super manager command
    /// </summary>
    public class CreateExternalSuperManagerCommandHandler : RuleBase, IRequestHandler<CreateExternalSuperManagerCommand, IdentityDto>
    {
        private readonly IUnitOfWork _unitOfWork;
        private readonly IIdentityDxos _identityDxos;
        private readonly IUserDxos _userDxos;
        private readonly IIdentityRuleChecker _identityRuleChecker;
        private readonly ICommonRuleChecker _commonRuleChecker;
        private readonly IUserRuleChecker _userRuleChecker;
        private readonly IUserClaimsPrincipalFactory<IdentityDto> _userClaimsPrincipalFactory;
        private readonly IGraphService _graphService;
        private readonly ITranslationService _translationService;
        private readonly IHttpContextAccessor _httpContextAccessor;
        private readonly IMediator _mediator;

        public CreateExternalSuperManagerCommandHandler(
            IUnitOfWork unitOfWork,
            IIdentityDxos identityDxos,
            IUserDxos userDxos,
            IIdentityRuleChecker identityChecker,
            IUserRuleChecker userRuleChecker,
            ICommonRuleChecker commonRuleChecker,
            IUserClaimsPrincipalFactory<IdentityDto> userClaimsPrincipalFactory,
            IGraphService graphService,
            ITranslationService translationService,
            IHttpContextAccessor httpContextAccessor,
            IMediator mediator)
        {
            _unitOfWork = unitOfWork;
            _identityDxos = identityDxos;
            _userDxos = userDxos;
            _identityRuleChecker = identityChecker;
            _commonRuleChecker = commonRuleChecker;
            _userRuleChecker = userRuleChecker;
            _userClaimsPrincipalFactory = userClaimsPrincipalFactory;
            _graphService = graphService;
            _translationService = translationService;
            _httpContextAccessor = httpContextAccessor;
            _mediator = mediator;           
        }

        public async Task<IdentityDto> Handle(CreateExternalSuperManagerCommand request, CancellationToken cancellationToken)
        {    
            // Check Business Rules
            CheckRule(new ValidEmailRule(_translationService, _identityRuleChecker, request.Email));
            CheckRule(new IdentityEmailMustbeUniqueRule(_translationService, _identityRuleChecker, request.Email));
            CheckRule(new GuidShouldNotBeEmptyRule(_translationService, _commonRuleChecker, request.CountryId, nameof(request.CountryId)));
            CheckRule(new SingleSuperManagerPerCountryRule(_translationService, _userRuleChecker, request.CountryId));
            CheckRule(new UserShouldBeWhoAdminRule(_translationService, _userRuleChecker, request.CurrentUserId));

            // create identity
            Identity identity = new Identity(request.Email, request.Email, Constants.IdentityConstant.External, false);
            _unitOfWork.IdentityRepository.Add(identity);

            // create user
            User user = new User(identity.Id, request.Name, request.OrganizationName);
            _unitOfWork.UserRepository.Add(user);

            // User Country Access Mapping          
            UserCountryAccess userCountryAccess = new UserCountryAccess(user.Id, request.CountryId, (int)UserRoleEnum.SuperManager)
            {
                Status = (int)UserCountryAccessRightsEnum.InvitationNotAccepted
            };

            _unitOfWork.UserCountryAccessRepository.Add(userCountryAccess);

            // after super manager is created, send the activation link
            await SendUserActivation(request, userCountryAccess.Id);

            IdentityDto identityDto = _identityDxos.MapIdentityDto(identity);
            identityDto.User = _userDxos.MapUserDto(user);
            identityDto.RequestedCountryIds = new[] { request.CountryId };

            _ = _userClaimsPrincipalFactory.CreateAsync(identityDto);

            if (await _unitOfWork.CommitAsync(cancellationToken) == 0)
            {
                throw new ApplicationException();
            }

            return identityDto;
        }

        /// <summary>
        /// Sends activation email to the super manager user
        /// </summary>
        /// <param name="command">Object of CreateExternalSuperManagerCommand class</param>
        /// <param name="userCountryAccessId">Country id for which the user will be having an access</param>
        /// <returns>Task</returns>
        private async Task SendUserActivation(CreateExternalSuperManagerCommand command, Guid userCountryAccessId)
        {
            User currentUser = await _unitOfWork.UserRepository.Queryable(u => u.Id == command.CurrentUserId)
                                                               .Include(u => u.Identity)
                                                               .SingleAsync();

            Country country = await _unitOfWork.CountryRepository.Queryable(c => c.Id == command.CountryId).SingleAsync();

            await _mediator.Publish(new UserInvitationEmailNotification(command.Name, command.Email, country.Name, currentUser.Identity.Username, userCountryAccessId));
                       
        }
    }
}

﻿using FluentValidation;

namespace WHO.MALARIA.Domain.AssessmentResponseModels.DeskReview.Objective_2.Indicator_2_3_1
{
    class Response_1_Validator : AbstractValidator<Response_1>
    {
        /// <summary>
        /// Contains validation rules for indicator 2.3.1
        /// </summary>
        public Response_1_Validator()
        {
            //Sets CascadeMode for all the rules within this validator
            CascadeMode = CascadeMode.StopOnFirstFailure;

            //CannotBeAssessedReason should be validated only if CannotBeAssessed check box is checked
            RuleFor(x => x.CannotBeAssessedReason)
            .NotEmpty().When(x => x.CannotBeAssessed == true);

            //Check for other rules only if 'CannotBeAssessed' is false
            When(x => x.CannotBeAssessed == false, () =>
            {
                RuleFor(x => x).Must(k => !string.IsNullOrWhiteSpace(k.Guideline1.GuideLineName) || !string.IsNullOrWhiteSpace(k.Guideline2.GuideLineName) || !string.IsNullOrWhiteSpace(k.Guideline3.GuideLineName) || !string.IsNullOrWhiteSpace(k.Guideline4.GuideLineName))
                .WithMessage("At least one guideline name is mandatory");

                When(x => x.ParentData.CannotBeAssessed.GetValueOrDefault() == false, () =>
                {
                    #region Public - Public
                    //When(x => x.ParentData?.HealthSector.PublicHealthSector.PublicReportingDetails.ReportingMalaria == true, () =>
                    //{
                    //    RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline1.GuideLineName, z.HealthSector.PublicHealthSector.PublicReportingDetails.IsFirstGuidelineReceived)).WithMessage("Please select YES/NO for the first received guidelines of public health sector");
                    //    RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline1.GuideLineName, z.HealthSector.PublicHealthSector.PublicReportingDetails.FirstGuidelineDetails)).WithMessage("First received guideline details field is mandatory for public health sector");

                    //    RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline2.GuideLineName, z.HealthSector.PublicHealthSector.PublicReportingDetails.IsSecondGuidelineReceived)).WithMessage("Please select YES/NO for the second received guidelines of public health sector");
                    //    RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline2.GuideLineName, z.HealthSector.PublicHealthSector.PublicReportingDetails.SecondGuidelineDetails)).WithMessage("Second received guideline details field is mandatory for public health sector");

                    //    RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline3.GuideLineName, z.HealthSector.PublicHealthSector.PublicReportingDetails.IsThirdGuidelineReceived)).WithMessage("Please select YES/NO for the third received guidelines of public health sector");
                    //    RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline3.GuideLineName, z.HealthSector.PublicHealthSector.PublicReportingDetails.ThirdGuidelineDetails)).WithMessage("Third received guideline details field is mandatory for public health sector");

                    //    RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline4.GuideLineName, z.HealthSector.PublicHealthSector.PublicReportingDetails.IsFourthGuidelineReceived)).WithMessage("Please select YES/NO for the fourth received guidelines of public health sector");
                    //    RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline4.GuideLineName, z.HealthSector.PublicHealthSector.PublicReportingDetails.FourthGuidelineDetails)).WithMessage("Fourth received guideline details field is mandatory for public health sector");
                    //});
                    #endregion

                    #region Public - Health facility
                    When(x => x.ParentData?.HealthSector.PublicHealthSector.HealthFacility.ReportingMalaria == true, () =>
                    {
                        RuleFor(x => x).Must(z => HasFirstGuidelineValue(z.Guideline1.GuideLineName, z.HealthSector.PublicHealthSector.HealthFacility.IsFirstGuidelineReceived)).WithMessage("Please select YES/NO for the first received guidelines of public health facility health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline1.GuideLineName, z.HealthSector.PublicHealthSector.HealthFacility.FirstGuidelineDetails, z.HealthSector.PublicHealthSector.HealthFacility.IsFirstGuidelineReceived)).WithMessage("First received guideline details field is mandatory for public health facility health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline2.GuideLineName, z.HealthSector.PublicHealthSector.HealthFacility.IsSecondGuidelineReceived)).WithMessage("Please select YES/NO for the second received guidelines of public health facility health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline2.GuideLineName, z.HealthSector.PublicHealthSector.HealthFacility.SecondGuidelineDetails)).WithMessage("Second received guideline details field is mandatory for public health facility health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline3.GuideLineName, z.HealthSector.PublicHealthSector.HealthFacility.IsThirdGuidelineReceived)).WithMessage("Please select YES/NO for the third received guidelines of public health facility health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline3.GuideLineName, z.HealthSector.PublicHealthSector.HealthFacility.ThirdGuidelineDetails)).WithMessage("Third received guideline details field is mandatory for public health facility health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline4.GuideLineName, z.HealthSector.PublicHealthSector.HealthFacility.IsFourthGuidelineReceived)).WithMessage("Please select YES/NO for the fourth received guidelines of public health facility health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline4.GuideLineName, z.HealthSector.PublicHealthSector.HealthFacility.FourthGuidelineDetails)).WithMessage("Fourth received guideline details field is mandatory for public health facility health sector");
                    });
                    #endregion

                    #region Public - Laboratory
                    When(x => x.ParentData?.HealthSector.PublicHealthSector.Laboratory.ReportingMalaria == true, () =>
                    {
                        RuleFor(x => x).Must(z => HasFirstGuidelineValue(z.Guideline1.GuideLineName, z.HealthSector.PublicHealthSector.Laboratory.IsFirstGuidelineReceived)).WithMessage("Please select YES/NO for the first received guidelines of public laboratory health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline1.GuideLineName, z.HealthSector.PublicHealthSector.Laboratory.FirstGuidelineDetails, z.HealthSector.PublicHealthSector.Laboratory.IsFirstGuidelineReceived)).WithMessage("First received guideline details field is mandatory for public laboratory health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline2.GuideLineName, z.HealthSector.PublicHealthSector.Laboratory.IsSecondGuidelineReceived)).WithMessage("Please select YES/NO for the second received guidelines of public laboratory health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline2.GuideLineName, z.HealthSector.PublicHealthSector.Laboratory.SecondGuidelineDetails)).WithMessage("Second received guideline details field is mandatory for public laboratory health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline3.GuideLineName, z.HealthSector.PublicHealthSector.Laboratory.IsThirdGuidelineReceived)).WithMessage("Please select YES/NO for the third received guidelines of public laboratory health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline3.GuideLineName, z.HealthSector.PublicHealthSector.Laboratory.ThirdGuidelineDetails)).WithMessage("Third received guideline details field is mandatory for public laboratory health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline4.GuideLineName, z.HealthSector.PublicHealthSector.Laboratory.IsFourthGuidelineReceived)).WithMessage("Please select YES/NO for the fourth received guidelines of public laboratory health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline4.GuideLineName, z.HealthSector.PublicHealthSector.Laboratory.FourthGuidelineDetails)).WithMessage("Fourth received guideline details field is mandatory for public laboratory health sector");
                    });
                    #endregion

                    #region Public - Hospital
                    When(x => x.ParentData?.HealthSector.PublicHealthSector.Hospital.ReportingMalaria == true, () =>
                    {
                        RuleFor(x => x).Must(z => HasFirstGuidelineValue(z.Guideline1.GuideLineName, z.HealthSector.PublicHealthSector.Hospital.IsFirstGuidelineReceived)).WithMessage("Please select YES/NO for the first received guidelines of public hospital health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline1.GuideLineName, z.HealthSector.PublicHealthSector.Hospital.FirstGuidelineDetails, z.HealthSector.PublicHealthSector.Hospital.IsFirstGuidelineReceived)).WithMessage("First received guideline details field is mandatory for public hospital health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline2.GuideLineName, z.HealthSector.PublicHealthSector.Hospital.IsSecondGuidelineReceived)).WithMessage("Please select YES/NO for the second received guidelines of public hospital health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline2.GuideLineName, z.HealthSector.PublicHealthSector.Hospital.SecondGuidelineDetails)).WithMessage("Second received guideline details field is mandatory for public hospital health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline3.GuideLineName, z.HealthSector.PublicHealthSector.Hospital.IsThirdGuidelineReceived)).WithMessage("Please select YES/NO for the third received guidelines of public hospital health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline3.GuideLineName, z.HealthSector.PublicHealthSector.Hospital.ThirdGuidelineDetails)).WithMessage("Third received guideline details field is mandatory for public hospital health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline4.GuideLineName, z.HealthSector.PublicHealthSector.Hospital.IsFourthGuidelineReceived)).WithMessage("Please select YES/NO for the fourth received guidelines of public hospital health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline4.GuideLineName, z.HealthSector.PublicHealthSector.Hospital.FourthGuidelineDetails)).WithMessage("Fourth received guideline details field is mandatory for public hospital health sector");
                    });
                    #endregion

                    #region Private formal - private formal
                   
                    //When(x => x.ParentData?.HealthSector.PrivateFormal.PrivateFormal.ReportingMalaria == true, () =>
                    //{
                    //    RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.PrivateFormal.IsFirstGuidelineReceived)).WithMessage("Please select YES/NO for the first received guidelines of private formal health sector");
                    //    RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.PrivateFormal.FirstGuidelineDetails)).WithMessage("First received guideline details field is mandatory for private formal health sector");

                    //    RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.PrivateFormal.IsSecondGuidelineReceived)).WithMessage("Please select YES/NO for the second received guidelines of private formal health sector");
                    //    RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.PrivateFormal.SecondGuidelineDetails)).WithMessage("Second received guideline details field is mandatory for private formal health sector");

                    //    RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.PrivateFormal.IsThirdGuidelineReceived)).WithMessage("Please select YES/NO for the third received guidelines of private formal health sector");
                    //    RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.PrivateFormal.ThirdGuidelineDetails)).WithMessage("Third received guideline details field is mandatory for private formal health sector");

                    //    RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.PrivateFormal.IsFourthGuidelineReceived)).WithMessage("Please select YES/NO for the fourth received guidelines of private formal health sector");
                    //    RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.PrivateFormal.FourthGuidelineDetails)).WithMessage("Fourth received guideline details field is mandatory for private formal health sector");
                    //});

                    #endregion

                    #region Private formal - Hospital
                    When(x => x.ParentData?.HealthSector.PrivateFormal.Hospital.ReportingMalaria == true, () =>
                    {
                        RuleFor(x => x).Must(z => HasFirstGuidelineValue(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.Hospital.IsFirstGuidelineReceived)).WithMessage("Please select YES/NO for the first received guidelines of private formal hospital health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.Hospital.FirstGuidelineDetails, z.HealthSector.PrivateFormal.Hospital.IsFirstGuidelineReceived)).WithMessage("First received guideline details field is mandatory for private formal hospital health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.Hospital.IsSecondGuidelineReceived)).WithMessage("Please select YES/NO for the second received guidelines of private formal hospital health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.Hospital.SecondGuidelineDetails)).WithMessage("Second received guideline details field is mandatory for private formal hospital health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.Hospital.IsThirdGuidelineReceived)).WithMessage("Please select YES/NO for the third received guidelines of private formal hospital health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.Hospital.ThirdGuidelineDetails)).WithMessage("Third received guideline details field is mandatory for private formal hospital health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.Hospital.IsFourthGuidelineReceived)).WithMessage("Please select YES/NO for the fourth received guidelines of private formal hospital health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.Hospital.FourthGuidelineDetails)).WithMessage("Fourth received guideline details field is mandatory for private formal hospital health sector");
                    });
                    #endregion

                   

                    #region Private formal - Health facility
                    When(x => x.ParentData?.HealthSector.PrivateFormal.HealthFacility.ReportingMalaria == true, () =>
                    {
                        RuleFor(x => x).Must(z => HasFirstGuidelineValue(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.HealthFacility.IsFirstGuidelineReceived)).WithMessage("Please select YES/NO for the first received guidelines of private formal health facility health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.HealthFacility.FirstGuidelineDetails, z.HealthSector.PrivateFormal.HealthFacility.IsFirstGuidelineReceived)).WithMessage("First received guideline details field is mandatory for private formal health facility health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.HealthFacility.IsSecondGuidelineReceived)).WithMessage("Please select YES/NO for the second received guidelines of private formal health facility health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.HealthFacility.SecondGuidelineDetails)).WithMessage("Second received guideline details field is mandatory for private formal health facility health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.HealthFacility.IsThirdGuidelineReceived)).WithMessage("Please select YES/NO for the third received guidelines of private formal health facility health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.HealthFacility.ThirdGuidelineDetails)).WithMessage("Third received guideline details field is mandatory for private formal health facility health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.HealthFacility.IsFourthGuidelineReceived)).WithMessage("Please select YES/NO for the fourth received guidelines of private formal health facility health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.HealthFacility.FourthGuidelineDetails)).WithMessage("Fourth received guideline details field is mandatory for private formal health facility health sector");
                    });
                    #endregion

                    #region Private formal - Laboratory
                    When(x => x.ParentData?.HealthSector.PrivateFormal.Laboratory.ReportingMalaria == true, () =>
                    {
                        RuleFor(x => x).Must(z => HasFirstGuidelineValue(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.Laboratory.IsFirstGuidelineReceived)).WithMessage("Please select YES/NO for the first received guidelines of private formal laboratory health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.Laboratory.FirstGuidelineDetails, z.HealthSector.PrivateFormal.Laboratory.IsFirstGuidelineReceived)).WithMessage("First received guideline details field is mandatory for private formal laboratory health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.Laboratory.IsSecondGuidelineReceived)).WithMessage("Please select YES/NO for the second received guidelines of private formal laboratory health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.Laboratory.SecondGuidelineDetails)).WithMessage("Second received guideline details field is mandatory for private formal laboratory health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.Laboratory.IsThirdGuidelineReceived)).WithMessage("Please select YES/NO for the third received guidelines of private formal laboratory health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.Laboratory.ThirdGuidelineDetails)).WithMessage("Third received guideline details field is mandatory for private formal laboratory health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.Laboratory.IsFourthGuidelineReceived)).WithMessage("Please select YES/NO for the fourth received guidelines of private formal laboratory health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.Laboratory.FourthGuidelineDetails)).WithMessage("Fourth received guideline details field is mandatory for private formal laboratory health sector");
                    });
                    #endregion

                    #region Private formal - Faith based clinic
                    When(x => x.ParentData?.HealthSector.PrivateFormal.FaithBasedClinic.ReportingMalaria == true, () =>
                    {
                        RuleFor(x => x).Must(z => HasFirstGuidelineValue(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.FaithBasedClinic.IsFirstGuidelineReceived)).WithMessage("Please select YES/NO for the first received guidelines of private formal faith based clinic health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.FaithBasedClinic.FirstGuidelineDetails, z.HealthSector.PrivateFormal.FaithBasedClinic.IsFirstGuidelineReceived)).WithMessage("First received guideline details field is mandatory for private formal faith based clinic health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.FaithBasedClinic.IsSecondGuidelineReceived)).WithMessage("Please select YES/NO for the second received guidelines of private formal faith based clinic health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.FaithBasedClinic.SecondGuidelineDetails)).WithMessage("Second received guideline details field is mandatory for private formal faith based clinic health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.FaithBasedClinic.IsThirdGuidelineReceived)).WithMessage("Please select YES/NO for the third received guidelines of private formal faith based clinic health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.FaithBasedClinic.ThirdGuidelineDetails)).WithMessage("Third received guideline details field is mandatory for private formal faith based clinic health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.FaithBasedClinic.IsFourthGuidelineReceived)).WithMessage("Please select YES/NO for the fourth received guidelines of private formal faith based clinic health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.FaithBasedClinic.FourthGuidelineDetails)).WithMessage("Fourth received guideline details field is mandatory for private formal faith based clinic health sector");
                    });
                    #endregion

                    #region Private formal - NGO clinic
                    When(x => x.ParentData?.HealthSector.PrivateFormal.NGOClinic.ReportingMalaria == true, () =>
                    {
                        RuleFor(x => x).Must(z => HasFirstGuidelineValue(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.NGOClinic.IsFirstGuidelineReceived)).WithMessage("Please select YES/NO for the first received guidelines of private formal NGO clinic health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.NGOClinic.FirstGuidelineDetails, z.HealthSector.PrivateFormal.NGOClinic.IsFirstGuidelineReceived)).WithMessage("First received guideline details field is mandatory for private formal NGO clinic health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.NGOClinic.IsSecondGuidelineReceived)).WithMessage("Please select YES/NO for the second received guidelines of private formal NGO clinic health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.NGOClinic.SecondGuidelineDetails)).WithMessage("Second received guideline details field is mandatory for private formal NGO clinic health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.NGOClinic.IsThirdGuidelineReceived)).WithMessage("Please select YES/NO for the third received guidelines of private formal NGO clinic health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.NGOClinic.ThirdGuidelineDetails)).WithMessage("Third received guideline details field is mandatory for private formal NGO clinic health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.NGOClinic.IsFourthGuidelineReceived)).WithMessage("Please select YES/NO for the fourth received guidelines of private formal NGO clinic health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.NGOClinic.FourthGuidelineDetails)).WithMessage("Fourth received guideline details field is mandatory for private formal NGO clinic health sector");
                    });
                    #endregion

                    #region Private formal - Military
                    When(x => x.ParentData?.HealthSector.PrivateFormal.Military.ReportingMalaria == true, () =>
                    {
                        RuleFor(x => x).Must(z => HasFirstGuidelineValue(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.Military.IsFirstGuidelineReceived)).WithMessage("Please select YES/NO for the first received guidelines of private formal Military health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.Military.FirstGuidelineDetails, z.HealthSector.PrivateFormal.Military.IsFirstGuidelineReceived)).WithMessage("First received guideline details field is mandatory for private formal Military health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.Military.IsSecondGuidelineReceived)).WithMessage("Please select YES/NO for the second received guidelines of private formal Military health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.Military.SecondGuidelineDetails)).WithMessage("Second received guideline details field is mandatory for private formal Military health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.Military.IsThirdGuidelineReceived)).WithMessage("Please select YES/NO for the third received guidelines of private formal Military health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.Military.ThirdGuidelineDetails)).WithMessage("Third received guideline details field is mandatory for private formal Military health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.Military.IsFourthGuidelineReceived)).WithMessage("Please select YES/NO for the fourth received guidelines of private formal Military health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.Military.FourthGuidelineDetails)).WithMessage("Fourth received guideline details field is mandatory for private formal Military health sector");
                    });
                    #endregion

                    #region Private formal - Police
                    When(x => x.ParentData?.HealthSector.PrivateFormal.Police.ReportingMalaria == true, () =>
                    {
                        RuleFor(x => x).Must(z => HasFirstGuidelineValue(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.Police.IsFirstGuidelineReceived)).WithMessage("Please select YES/NO for the first received guidelines of private formal Police health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.Police.FirstGuidelineDetails, z.HealthSector.PrivateFormal.Police.IsFirstGuidelineReceived)).WithMessage("First received guideline details field is mandatory for private formal Police health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.Police.IsSecondGuidelineReceived)).WithMessage("Please select YES/NO for the second received guidelines of private formal Police health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.Police.SecondGuidelineDetails)).WithMessage("Second received guideline details field is mandatory for private formal Police health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.Police.IsThirdGuidelineReceived)).WithMessage("Please select YES/NO for the third received guidelines of private formal Police health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.Police.ThirdGuidelineDetails)).WithMessage("Third received guideline details field is mandatory for private formal Police health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.Police.IsFourthGuidelineReceived)).WithMessage("Please select YES/NO for the fourth received guidelines of private formal Police health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.Police.FourthGuidelineDetails)).WithMessage("Fourth received guideline details field is mandatory for private formal Police health sector");
                    });
                    #endregion

                    #region Private formal - Prison
                    When(x => x.ParentData?.HealthSector.PrivateFormal.Prison.ReportingMalaria == true, () =>
                    {
                        RuleFor(x => x).Must(z => HasFirstGuidelineValue(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.Prison.IsFirstGuidelineReceived)).WithMessage("Please select YES/NO for the first received guidelines of private formal Prison health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline1.GuideLineName, z.HealthSector.PrivateFormal.Prison.FirstGuidelineDetails, z.HealthSector.PrivateFormal.Prison.IsFirstGuidelineReceived)).WithMessage("First received guideline details field is mandatory for private formal Prison health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.Prison.IsSecondGuidelineReceived)).WithMessage("Please select YES/NO for the second received guidelines of private formal Prison health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline2.GuideLineName, z.HealthSector.PrivateFormal.Prison.SecondGuidelineDetails)).WithMessage("Second received guideline details field is mandatory for private formal Prison health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.Prison.IsThirdGuidelineReceived)).WithMessage("Please select YES/NO for the third received guidelines of private formal Prison health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline3.GuideLineName, z.HealthSector.PrivateFormal.Prison.ThirdGuidelineDetails)).WithMessage("Third received guideline details field is mandatory for private formal Prison health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.Prison.IsFourthGuidelineReceived)).WithMessage("Please select YES/NO for the fourth received guidelines of private formal Prison health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline4.GuideLineName, z.HealthSector.PrivateFormal.Prison.FourthGuidelineDetails)).WithMessage("Fourth received guideline details field is mandatory for private formal Prison health sector");
                    });
                    #endregion

                    #region Private informal
                    When(x => x.ParentData?.HealthSector.PrivateInformal.ReportingMalaria == true, () =>
                    {
                        RuleFor(x => x).Must(z => HasFirstGuidelineValue(z.Guideline1.GuideLineName, z.HealthSector.PrivateInformal.IsFirstGuidelineReceived)).WithMessage("Please select YES/NO for the first received guidelines of private informal health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline1.GuideLineName, z.HealthSector.PrivateInformal.FirstGuidelineDetails, z.HealthSector.PrivateInformal.IsFirstGuidelineReceived)).WithMessage("First received guideline details field is mandatory for private informal health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline2.GuideLineName, z.HealthSector.PrivateInformal.IsSecondGuidelineReceived)).WithMessage("Please select YES/NO for the second received guidelines of private informal health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline2.GuideLineName, z.HealthSector.PrivateInformal.SecondGuidelineDetails)).WithMessage("Second received guideline details field is mandatory for private informal health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline3.GuideLineName, z.HealthSector.PrivateInformal.IsThirdGuidelineReceived)).WithMessage("Please select YES/NO for the third received guidelines of private informal health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline3.GuideLineName, z.HealthSector.PrivateInformal.ThirdGuidelineDetails)).WithMessage("Third received guideline details field is mandatory for private informal health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline4.GuideLineName, z.HealthSector.PrivateInformal.IsFourthGuidelineReceived)).WithMessage("Please select YES/NO for the fourth received guidelines of private informal health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline4.GuideLineName, z.HealthSector.PrivateInformal.FourthGuidelineDetails)).WithMessage("Fourth received guideline details field is mandatory for private informal health sector");
                    });
                    #endregion

                    #region Community 
                    When(x => x.ParentData?.HealthSector.Community.ReportingMalaria == true, () =>
                    {
                        RuleFor(x => x).Must(z => HasFirstGuidelineValue(z.Guideline1.GuideLineName, z.HealthSector.Community.IsFirstGuidelineReceived)).WithMessage("Please select YES/NO for the first received guidelines of community health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline1.GuideLineName, z.HealthSector.Community.FirstGuidelineDetails, z.HealthSector.Community.IsFirstGuidelineReceived)).WithMessage("First received guideline details field is mandatory for community health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline2.GuideLineName, z.HealthSector.Community.IsSecondGuidelineReceived)).WithMessage("Please select YES/NO for the second received guidelines of community health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline2.GuideLineName, z.HealthSector.Community.SecondGuidelineDetails)).WithMessage("Second received guideline details field is mandatory for community health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline3.GuideLineName, z.HealthSector.Community.IsThirdGuidelineReceived)).WithMessage("Please select YES/NO for the third received guidelines of community health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline3.GuideLineName, z.HealthSector.Community.ThirdGuidelineDetails)).WithMessage("Third received guideline details field is mandatory for community health sector");

                        RuleFor(x => x).Must(z => HasGuidelineValue(z.Guideline4.GuideLineName, z.HealthSector.Community.IsFourthGuidelineReceived)).WithMessage("Please select YES/NO for the fourth received guidelines of community health sector");
                        RuleFor(x => x).Must(z => HasGuidelineDetailsNullOrEmpty(z.Guideline4.GuideLineName, z.HealthSector.Community.FourthGuidelineDetails)).WithMessage("Fourth received guideline details field is mandatory for community health sector");
                    });
                    #endregion
                });
            });
        }

        private bool HasGuidelineValue(string guideline, bool? isGuidelineReceived) => string.IsNullOrWhiteSpace(guideline) ? true : isGuidelineReceived.HasValue;

        private bool HasFirstGuidelineValue(string guideline, bool? isGuidelineReceived) => string.IsNullOrWhiteSpace(guideline) ? true : (isGuidelineReceived.HasValue || isGuidelineReceived == null);

        private bool HasGuidelineDetailsNullOrEmpty(string guidelineName, string details, bool? isGuidelineReceived) => string.IsNullOrWhiteSpace(guidelineName) ? true : (isGuidelineReceived == null || !string.IsNullOrWhiteSpace(details));
        
        private bool HasGuidelineDetailsNullOrEmpty(string guidelineName, string details) => string.IsNullOrWhiteSpace(guidelineName) ? true : !string.IsNullOrWhiteSpace(details);
    }
}

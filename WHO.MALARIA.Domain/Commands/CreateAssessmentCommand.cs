﻿using MediatR;
using Newtonsoft.Json;
using System;

namespace WHO.MALARIA.Domain.Commands
{
    /// <summary>
    /// Properties related to assessment creation.
    /// </summary>
    public class CreateAssessmentCommand : IRequest<Guid>
    {
        [JsonProperty("countryId")]
        public Guid CountryId { get; set; }

        [JsonProperty("startDate")]
        public DateTime StartDate { get; set; }

        [JsonProperty("endDate")]
        public DateTime EndDate { get; set; }

        [<PERSON>sonProperty("manager")]
        public Guid? Manager { get; set; }

        [JsonProperty("editors")]
        public Guid[] Editors { get; set; }

        [JsonProperty("reviewers")]
        public Guid[] Reviewers { get; set; }
    }
}

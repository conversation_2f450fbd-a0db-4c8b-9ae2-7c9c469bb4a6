﻿using System;
using MediatR;
using Newtonsoft.Json;
using WHO.MALARIA.Domain.Enum;

namespace WHO.MALARIA.Domain.Commands
{
    /// <summary>
    /// Command to change user's details
    /// </summary>
    public class UpdateUserCommand : IRequest<bool>
    {
        [JsonProperty("userId")]
        public Guid UserId { get; set; }

        [JsonProperty("emailId")]
        public string EmailId { get; set; }

        [JsonProperty("countryRequestedForIds")]
        public Guid[] CountryRequestedForIds { get; set; }

        [JsonProperty("name")]
        public string Name { get; set; }

        [JsonProperty("organizationName")]
        public string OrganizationName { get; set; }

        [JsonProperty("userType")]
        public UserRoleEnum UserType { get; set; }     
    }
}

